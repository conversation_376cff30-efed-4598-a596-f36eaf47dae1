
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.** 



# nous allons compléter les tâches. 

- La fonction ajouter nouvelle plante peut être améliorée. Pourquoi ? L'utilisateur pourrait apporter un descriptif par écrit de ce qu'il constate. Ça serait un plus. 

- Pour le moment, dans la partie calendrier, impossible de créer un nouvel événement. Voir capture d'écran de la console. 

La page calendrier a un problème. Lorsqu'on clique sur le menu, si je fais par exemple nouvel onglet, la page n'aboutit pas, il ne s'affiche rien du tout. 
CalendarView.tsx:12 Uncaught SyntaxError: The requested module '/src/components/common/icons.tsx' does not provide an export named 'XIcon' (at CalendarView.tsx:12:46)



Commit GitHub =>> https://github.com/cisco-03/FloraSynth.git
Faites une vérification dans l'application. Aucun nom de "Violet Rikita" ne doit apparaître. L'application se nomme bien FloraSynth   **Le dossier Z-Archive aussi ne doit pas être commité, attention !**

Pour terminer, vérifiez que l'application est entièrement fonctionnelle, qu'il n'y a pas de problème de sécurité (_Framework d'Instructions), et ensuite, préparer l'application pour Netlify pour un déploiement. 
Attention, certains dossiers ne doivent pas être commités comme le framework d'instruction. Et bien sûr certains fichiers de sécurité comme les clés API qui doivent être en sécurité, etc. 







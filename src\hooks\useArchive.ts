import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { archiveService } from '../services/archiveService';
import { ArchiveData, ArchiveStats } from '../types';

/**
 * Hook pour gérer l'archivage automatique des données utilisateur
 * Intègre l'accès Gemini pour l'auto-apprentissage
 */
export const useArchive = () => {
  const { user } = useAuth();
  const [archives, setArchives] = useState<ArchiveData[]>([]);
  const [stats, setStats] = useState<ArchiveStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Charge les archives de l'utilisateur
   */
  const loadArchives = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const userArchives = await archiveService.getUserArchives(user.uid);
      setArchives(userArchives);

      const archiveStats = await archiveService.getArchiveStats(user.uid);
      setStats(archiveStats);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      console.error('❌ Erreur lors du chargement des archives:', errorMessage);
      setError('Impossible de charger les archives');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Déclenche l'archivage manuel d'une année spécifique
   */
  const archiveYear = useCallback(async (year: number) => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      await archiveService.archiveYearData(user.uid, year);
      await loadArchives(); // Recharger les archives
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de l\'archivage:', errorMessage);
      setError(`Impossible d'archiver les données de ${year}`);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, loadArchives]);

  /**
   * Récupère une archive spécifique
   */
  const getArchive = useCallback(async (year: number): Promise<ArchiveData | null> => {
    if (!user) return null;

    try {
      return await archiveService.getArchive(user.uid, year);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la récupération de l\'archive:', errorMessage);
      setError(`Impossible de récupérer l'archive ${year}`);
      return null;
    }
  }, [user]);

  /**
   * Vérifie et déclenche l'archivage automatique si nécessaire
   */
  const checkAutoArchive = useCallback(async () => {
    if (!user) return;

    try {
      await archiveService.checkAutoArchive(user.uid);
      // Recharger les archives après archivage automatique
      const userArchives = await archiveService.getUserArchives(user.uid);
      setArchives(userArchives);
      const archiveStats = await archiveService.getArchiveStats(user.uid);
      setStats(archiveStats);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la vérification d\'archivage automatique:', errorMessage);
    }
  }, [user]);

  /**
   * Obtient les années disponibles pour l'archivage
   */
  const getAvailableYears = useCallback((): number[] => {
    const currentYear = new Date().getFullYear();
    const archivedYears = archives.map(a => a.year);
    const availableYears: number[] = [];

    // Proposer les 5 dernières années non archivées
    for (let year = currentYear - 1; year >= currentYear - 5; year--) {
      if (!archivedYears.includes(year)) {
        availableYears.push(year);
      }
    }

    return availableYears;
  }, [archives]);

  /**
   * Formate les statistiques pour l'affichage
   */
  const getFormattedStats = useCallback(() => {
    if (!stats) return null;

    return {
      ...stats,
      archiveRange: stats.totalArchives > 0 
        ? `${stats.oldestArchive} - ${stats.newestArchive}`
        : 'Aucune archive',
      averagePlantsPerYear: stats.totalArchives > 0 
        ? Math.round(stats.totalArchivedPlants / stats.totalArchives)
        : 0,
      averageDiagnosticsPerYear: stats.totalArchives > 0 
        ? Math.round(stats.totalArchivedDiagnostics / stats.totalArchives)
        : 0
    };
  }, [stats]);

  // Charger les archives au montage du composant
  useEffect(() => {
    if (user) {
      loadArchives();
    }
  }, [user, loadArchives]);

  // Vérifier l'archivage automatique séparément
  useEffect(() => {
    if (user) {
      checkAutoArchive();
    }
  }, [user]); // Seulement dépendant de user

  return {
    // Données
    archives,
    stats,
    formattedStats: getFormattedStats(),
    availableYears: getAvailableYears(),
    
    // États
    isLoading,
    error,
    
    // Actions
    loadArchives,
    archiveYear,
    getArchive,
    checkAutoArchive,
    
    // Utilitaires
    clearError: () => setError(null)
  };
};

# 🚀 Guide de Déploiement FloraSynth

## 📋 Pré-requis

### Comptes nécessaires
- ✅ Compte GitHub
- ✅ Compte Netlify
- ✅ Projet Firebase configuré
- ✅ Clé API Google Gemini

### Vérifications avant déploiement
- ✅ Application compile sans erreur (`npm run build`)
- ✅ Aucune erreur TypeScript
- ✅ Dossiers sensibles exclus (`.gitignore` configuré)
- ✅ Variables d'environnement sécurisées

## 🔐 Configuration des Variables d'Environnement

### 1. Variables Firebase
```
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

### 2. Variable Gemini
```
VITE_GEMINI_API_KEY=your_gemini_api_key
```

## 📂 Préparation du Repository GitHub

### 1. Vérification des fichiers exclus
Les dossiers suivants sont automatiquement exclus du commit :
- `Z-Archives/` - Archives de développement
- `_Framework d'Instructions/` - Documentation interne
- `.env*` - Variables d'environnement
- `node_modules/` - Dépendances
- `dist/` - Build de production

### 2. Commandes Git
```bash
# Vérifier le statut
git status

# Ajouter les fichiers (les exclusions sont automatiques)
git add .

# Commit initial
git commit -m "feat: Application FloraSynth complète avec archivage automatique et intégration Gemini"

# Pousser vers GitHub
git push origin main
```

## 🌐 Déploiement Netlify

### 1. Connexion du Repository
1. Se connecter à [Netlify](https://netlify.com)
2. Cliquer sur "New site from Git"
3. Choisir GitHub et autoriser l'accès
4. Sélectionner le repository FloraSynth

### 2. Configuration Build
Netlify détecte automatiquement la configuration via `netlify.toml` :
- **Build command** : `npm run build`
- **Publish directory** : `dist`
- **Node version** : 18

### 3. Variables d'Environnement Netlify
Dans l'interface Netlify > Site settings > Environment variables :

```
VITE_FIREBASE_API_KEY = [votre_clé_firebase]
VITE_FIREBASE_AUTH_DOMAIN = [votre_domaine_firebase]
VITE_FIREBASE_PROJECT_ID = [votre_project_id]
VITE_FIREBASE_STORAGE_BUCKET = [votre_bucket]
VITE_FIREBASE_MESSAGING_SENDER_ID = [votre_sender_id]
VITE_FIREBASE_APP_ID = [votre_app_id]
VITE_GEMINI_API_KEY = [votre_clé_gemini]
```

### 4. Déploiement
1. Cliquer sur "Deploy site"
2. Attendre la fin du build (2-3 minutes)
3. Le site sera disponible sur l'URL fournie par Netlify

## 🔧 Configuration Post-Déploiement

### 1. Domaine personnalisé (optionnel)
1. Dans Netlify > Domain settings
2. Ajouter un domaine personnalisé
3. Configurer les DNS selon les instructions

### 2. HTTPS
- HTTPS est automatiquement activé par Netlify
- Certificat SSL gratuit via Let's Encrypt

### 3. Redirections
Les redirections SPA sont configurées automatiquement via `netlify.toml`

## 🛡️ Sécurité

### Headers de sécurité (automatiques)
- `Strict-Transport-Security` - Force HTTPS
- `X-Frame-Options` - Protection clickjacking
- `X-Content-Type-Options` - Protection XSS
- `Referrer-Policy` - Contrôle des référents

### Cache des assets
- Assets statiques : cache 1 an
- HTML : pas de cache (pour les mises à jour)

## 🔄 Déploiement Continu

### Automatisation
- Chaque push sur `main` déclenche un redéploiement automatique
- Build et tests automatiques
- Rollback possible en cas d'erreur

### Branches de développement
```bash
# Créer une branche feature
git checkout -b feature/nouvelle-fonctionnalite

# Développer et tester
# ...

# Merger vers main
git checkout main
git merge feature/nouvelle-fonctionnalite
git push origin main
```

## 📊 Monitoring

### Netlify Analytics
- Trafic et performance
- Erreurs de build
- Logs de déploiement

### Firebase Console
- Authentification des utilisateurs
- Base de données Firestore
- Stockage des images
- Quotas API

## 🚨 Dépannage

### Erreurs communes

**Build failed**
```bash
# Vérifier localement
npm run build

# Vérifier les variables d'environnement
# Elles doivent être définies dans Netlify
```

**404 sur les routes**
- Vérifier que `netlify.toml` contient les redirections SPA
- Redéployer si nécessaire

**Erreurs Firebase**
- Vérifier les variables d'environnement
- Vérifier les règles Firestore
- Vérifier les domaines autorisés dans Firebase Console

## ✅ Checklist de Déploiement

- [ ] Application compile sans erreur
- [ ] Variables d'environnement configurées
- [ ] Repository GitHub créé et poussé
- [ ] Site Netlify configuré
- [ ] Variables d'environnement Netlify définies
- [ ] Premier déploiement réussi
- [ ] Test de l'application en production
- [ ] Authentification Firebase fonctionne
- [ ] Upload d'images fonctionne
- [ ] Diagnostic Gemini fonctionne
- [ ] Archivage automatique configuré

## 📞 Support

En cas de problème :
1. Vérifier les logs Netlify
2. Vérifier la console Firebase
3. Tester localement avec les mêmes variables d'environnement
4. Consulter la documentation officielle

---

**L'application FloraSynth est maintenant prête pour la production !** 🌱✨
